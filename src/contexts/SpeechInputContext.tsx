import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  type ReactNode,
} from "react";
import {
  transcriptionService,
  normalize,
} from "../services/TranscriptionService";
import type {
  TranscriptionEvent,
  GameResponseType,
  SpeechInputState,
  SpeechInputContextProps
} from "../models";
import { gameResponseValidator } from '../services/GameResponseValidationService';

// ========== CONTEXT ==========
const SpeechInputContext = createContext<SpeechInputContextProps | undefined>(undefined);

export const useSpeechInput = () => {
  const context = useContext(SpeechInputContext);
  if (!context) {
    throw new Error("useSpeechInput must be used within SpeechInputProvider");
  }
  return context;
};

// ========== IMPROVED PROVIDER ==========
export const SpeechInputProvider = ({ children }: { children: ReactNode }) => {
  // ========== STATE ==========
  const [state, setState] = useState<SpeechInputState>({
    transcription: null,
    isListening: false,
    isProcessing: false,
    confidence: 0,
    lastValidatedResponse: null,
    errorMessage: null,
  });

  // ========== REFS ==========
  const abortControllerRef = useRef<AbortController | null>(null);

  // ========== MEMOIZED VALUES ==========
  const supportedResponses = useMemo(() =>
    gameResponseValidator.getSupportedResponses(), []);

  const responseHelp = useMemo(() =>
    gameResponseValidator.getResponseHelp(), []);

  // ========== IMPROVED EVENT HANDLING ==========
  const handleTranscriptionEvent = useCallback((event: TranscriptionEvent) => {
    // log.debug("speechInput", `📝 Evento recibido: ${event.type}`, {
    //   data: event.data.substring(0, 50),
    // });

    switch (event.type) {
      case "transcription": {
        const transcriptionText = event.normalized || event.data;
        const validation = gameResponseValidator.validate(transcriptionText);

        setState(prev => ({
          ...prev,
          transcription: transcriptionText,
          isProcessing: false,
          errorMessage: null,
          lastValidatedResponse: validation.type,
          confidence: validation.confidence
        }));
        break;
      }

      case "error":
        setState(prev => ({
          ...prev,
          isProcessing: false,
          errorMessage: event.data,
        }));
        break;

      case "command":
        // log.info("speechInput", `🎯 Comando ejecutado: ${event.data}`);
        break;
    }
  }, []);

  // ========== EFFECTS ==========
  useEffect(() => {
    // log.info("speechInput", "🎤 Inicializando SpeechInputProvider");

    // 🔧 MEJORA: Eliminar polling, usar solo eventos
    const removeListener = transcriptionService.addEventListener(handleTranscriptionEvent);

    // Sincronizar estado inicial
    const initialListening = transcriptionService.isCurrentlyListening();
    setState(prev => ({ ...prev, isListening: initialListening }));

    return () => {
      // log.info("speechInput", "🧹 Limpiando SpeechInputProvider");
      removeListener();

      // Cancelar operaciones pendientes
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [handleTranscriptionEvent]);

  // ========== BASIC CONTROLS ==========
  const startListening = useCallback(() => {
    // log.info("speechInput", "🎤 Iniciando escucha");

    setState(prev => ({ ...prev, isProcessing: true, errorMessage: null }));

    try {
      transcriptionService.startListening();
      setState(prev => ({ ...prev, isListening: true, isProcessing: false }));
    } catch (error) {
      // log.error("speechInput", "❌ Error iniciando escucha", error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        errorMessage: "Error al iniciar el micrófono"
      }));
    }
  }, []);

  const stopListening = useCallback(() => {
    // log.info("speechInput", "🛑 Deteniendo escucha");

    try {
      transcriptionService.stopListening();
      setState(prev => ({ ...prev, isListening: false }));
    } catch (error) {
      // log.error("speechInput", "❌ Error deteniendo escucha", error);
    }
  }, []);

  const clearTranscription = useCallback(() => {
    // log.debug("speechInput", "🧹 Limpiando transcripción");

    transcriptionService.clearTranscription();
    setState(prev => ({
      ...prev,
      transcription: null,
      lastValidatedResponse: null,
      errorMessage: null,
      confidence: 0
    }));
  }, []);

  const reset = useCallback(() => {
    // log.info("speechInput", "🔄 Reseteando SpeechInput");

    // Cancelar operaciones pendientes
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    transcriptionService.reset();
    setState({
      transcription: null,
      isListening: false,
      isProcessing: false,
      confidence: 0,
      lastValidatedResponse: null,
      errorMessage: null,
    });
  }, []);

  // ========== TESTING & SIMULATION ==========
  const simulateTranscription = useCallback((text: string) => {
    // log.debug("speechInput", `🎭 Simulando transcripción: ${text}`);
    transcriptionService.simulateTranscription(text);
  }, []);

  // ========== IMPROVED GAME-SPECIFIC FUNCTIONALITY ==========
  const validateGameResponse = useCallback((text: string): GameResponseType => {
    return gameResponseValidator.getResponseType(text);
  }, []);

  const getConfidenceScore = useCallback((text: string): number => {
    return gameResponseValidator.getConfidenceScore(text);
  }, []);

  const getAlternativeResponses = useCallback((text: string): GameResponseType[] => {
    return gameResponseValidator.getAlternativeResponses(text);
  }, []);

  const isResponseAmbiguous = useCallback((text: string): boolean => {
    return gameResponseValidator.isAmbiguous(text);
  }, []);

  // ========== IMPROVED ASYNC OPERATIONS ==========
  const waitForValidResponse = useCallback((timeout: number = 30000): Promise<GameResponseType> => {
    return new Promise((resolve, reject) => {
      // log.info("speechInput", `⏳ Esperando respuesta válida (timeout: ${timeout}ms)`);

      // 🔧 MEJORA: Usar AbortController para mejor cleanup
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      let resolved = false;

      const handleTranscription = (event: TranscriptionEvent) => {
        if (resolved || abortController.signal.aborted || event.type !== "transcription") return;

        const validation = gameResponseValidator.validate(event.normalized || event.data);

        if (validation.type !== "invalid" && validation.confidence > 0.6) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();

          // log.success("speechInput", `✅ Respuesta válida recibida: ${validation.type} (${validation.confidence})`);
          resolve(validation.type);
        } else {
          // log.debug("speechInput", `⚠️ Respuesta inválida ignorada: ${event.data} (confidence: ${validation.confidence})`);
        }
      };

      const removeListener = transcriptionService.addEventListener(handleTranscription);

      const timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          abortController.abort();
          removeListener();
          // log.warn("speechInput", "⏰ Timeout esperando respuesta válida");
          reject(new Error("Timeout esperando respuesta válida"));
        }
      }, timeout);

      // Cleanup en abort
      abortController.signal.addEventListener('abort', () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();
          reject(new Error("Operation aborted"));
        }
      });
    });
  }, []);

  const waitForCustomResponse = useCallback((
    expectedResponses: string[],
    timeout: number = 30000
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      // log.info("speechInput", `⏳ Esperando respuestas específicas`, { expectedResponses });

      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      let resolved = false;

      const handleTranscription = (event: TranscriptionEvent) => {
        if (resolved || abortController.signal.aborted || event.type !== "transcription") return;

        const normalized = event.normalized || event.data;
        const matchesExpected = expectedResponses.length === 0 ||
          expectedResponses.some(expected =>
            normalized.toLowerCase().includes(expected.toLowerCase())
          );

        if (matchesExpected) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();

          // log.success("speechInput", `✅ Respuesta esperada recibida: ${normalized}`);
          resolve(normalized);
        } else {
          // log.debug("speechInput", `⚠️ Respuesta no esperada: ${normalized}`);
        }
      };

      const removeListener = transcriptionService.addEventListener(handleTranscription);

      const timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          abortController.abort();
          removeListener();
          // log.warn("speechInput", "⏰ Timeout esperando respuesta específica");
          reject(new Error("Timeout esperando respuesta específica"));
        }
      }, timeout);

      abortController.signal.addEventListener('abort', () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();
          reject(new Error("Operation aborted"));
        }
      });
    });
  }, []);

  // ========== EVENT HANDLING ==========
  const addEventListener = useCallback((callback: (event: TranscriptionEvent) => void) => {
    return transcriptionService.addEventListener(callback);
  }, []);

  // ========== UTILITIES ==========
  const normalizeText = useCallback(async (text: string): Promise<string> => {
    return await normalize(text);
  }, []);

  const getSupportedResponses = useCallback((): string[] => {
    return supportedResponses;
  }, [supportedResponses]);

  const getResponseHelp = useCallback((): string => {
    return responseHelp;
  }, [responseHelp]);

  // ========== CONTEXT VALUE ==========
  const contextValue: SpeechInputContextProps = {
    // Current state
    state,

    // Basic controls
    startListening,
    stopListening,
    clearTranscription,
    reset,

    // Testing & simulation
    simulateTranscription,

    // Game-specific functionality
    validateGameResponse,
    waitForValidResponse,
    waitForCustomResponse,

    // Event handling
    addEventListener,

    // Utilities
    normalizeText,
    getSupportedResponses,
    getResponseHelp,

    // 🆕 NEW: Enhanced utilities
    getConfidenceScore,
    getAlternativeResponses,
    isResponseAmbiguous,
  };

  return (
    <SpeechInputContext.Provider value={contextValue}>
      {children}
    </SpeechInputContext.Provider>
  );
};
