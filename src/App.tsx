import { useState, useEffect, useCallback } from "react";
// Components
import { CookieConsentBanner } from "./components/CookieConsentBanner";
import { WelcomeScreen } from "./components/WelcomeScreen";
import { Header } from "./components/Header/Header";
import MainView from "./components/views/MainView";
import PlayView from "./components/views/PlayView";
import RulesView from "./components/views/RulesView";
import LivesView from "./components/views/LivesView";
import CluesView from "./components/views/CluesView";
import { LoadBlock } from "microapps";
// Contexts
import { useAppContext } from "./contexts/AppContext";
import { useGameOrchestrator } from "./contexts/GameOrchestratorContext";
// Types
import type { AppState, ViewMode } from "./models/app";
import type { GameMode } from "./models/game";
// Styles
import "./App.scss";

function App() {
  const { isInitialized, errors } = useAppContext();
  const { startGameFlow } = useGameOrchestrator();
  const [appState, setAppState] = useState<AppState>("loading");
  const [currentView, setCurrentView] = useState<ViewMode>("main");
  const [previousView, setPreviousView] = useState<ViewMode | null>(null);
  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);

  // ========== EFECTOS ==========
  useEffect(() => {
    if (isInitialized) {
      const hasConsent = localStorage.getItem("enygma_analytics_consent");

      if (!hasConsent) {
        setAppState("consent");
      } else {
        setAppState("welcome");
      }
    }
  }, [isInitialized]);

  useEffect(() => {
    if (errors.length > 0) {
      console.warn("⚠️ App: Errores detectados:", errors);
    }
  }, [errors]);

  // ========== HANDLERS ==========
  const navigateTo = useCallback((newView: ViewMode) => {
    setPreviousView(currentView);
    setCurrentView(newView);
  }, [currentView]);

  const navigateBack = useCallback(() => {
    if (previousView) {
      setCurrentView(previousView);
      setPreviousView(null);
    } else {
      setCurrentView("main");
    }
  }, [previousView]);

  const resetNavigationTo = useCallback((view: ViewMode) => {
    setPreviousView(null);
    setCurrentView(view);
  }, []);

  const handleAudioActivated = useCallback(() => {
    setAppState("welcome");
  }, []);

  const handleConsentGiven = useCallback(() => {
    setAppState("welcome");
  }, []);

  const handleWelcomeComplete = useCallback(() => {
    setAppState("ready");
    setHasPlayedWelcome(true);
  }, []);

  const handleStartGame = async (mode: GameMode) => {
    setIsStartingGame(true);

    try {
      if (!hasPlayedWelcome) {
        setHasPlayedWelcome(true);
      }

      await startGameFlow(mode);
      navigateTo("play");
    } catch (error) {
      console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  const handleGoHome = () => {
    // TODO: usar mh para volver al inicio con "closeWebView"
  };
  const handleShowRules = () => navigateTo("rules");
  const handleShowLives = () => navigateTo("lives");
  const handleShowClues = () => navigateTo("clues");
  const handleExistGame = () => resetNavigationTo("main");
  const handleBackToMain = () => navigateBack();
  const showBackButton = currentView !== "main" && currentView !== "play";

  // ========== RENDERIZADO CONDICIONAL ==========
  if (appState === "loading") {
    return (
      <div className="loader-container">
        <div className="loader">
          <LoadBlock interval={6000} text="Cargando..." />
        </div>
      </div>
    );
  }

  const renderContent = () => {
    switch (appState) {
      case "consent":
        return (
          <CookieConsentBanner
            onAudioActivated={handleAudioActivated}
            onConsentGiven={handleConsentGiven}
          />
        );
      case "welcome":
        return <WelcomeScreen onGameReady={handleWelcomeComplete} />;
    }

    switch (currentView) {
      case "main":
        return (
          <MainView
            handleStartGame={handleStartGame}
            handleShowRules={handleShowRules}
            isStartingGame={isStartingGame}
            isReady={true}
          />
        );
      case "play":
        return (
          <PlayView
            handleShowLives={handleShowLives}
            handleShowClues={handleShowClues}
            handleExistGame={handleExistGame}
          />
        );
      case "rules":
        return <RulesView isOpen={true} onClose={handleBackToMain} />;
      case "lives":
        return <LivesView />;
      case "clues":
        return <CluesView />;
      default:
        return <div className="content"></div>;
    }
  };

  return (
    <div className="App">
      <div className="game-container">
        <img
          src="assets/game/background.png"
          alt="Background"
          className="background"
        />

        <div className="board">
          <Header
            currentView={currentView}
            onBackToMain={handleBackToMain}
            onGoHome={handleGoHome}
            showBackButton={showBackButton}
          />

          {renderContent()}
        </div>
      </div>
    </div>
  );
}

export default App;
