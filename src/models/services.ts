// ========== TIPOS DE SERVICIOS AI ==========
export interface AIResponse {
  ok: boolean;
  output: string;
  sessionId: string;
  error?: string;
}

export interface APIPayload {
  id: {
    ses?: string;
    clt: string;
    corr: string;
  };
  preset: string;
  query: string;
  prompt_params: {
    preamble: string;
  };
  model_params: {
    max_tokens: number;
  };
}

// ========== TIPOS DE MHC (MOBILE HOST CONTAINER) ==========
export interface IMHC {
  setSucwTimeout(timeMS: number): void;
  closeWebView(): void;
  hideAura(): void;
  speakAura(text: string): void;
  onPageLoaded(): void;
  sendAura(text: string): void;
  getId(): string;
}

export interface MHCState {
  isAvailable: boolean;
  deviceId: string;
  connectionStatus: "disconnected" | "connecting" | "connected" | "error";
  capabilities: MHCCapabilities;
  errorMessage: string | null;
}

export interface MHCCapabilities {
  canSpeak: boolean;
  canCloseWebView: boolean;
  canSetTimeout: boolean;
  canHideAura: boolean;
  canSendMessages: boolean;
}

// ========== TIPOS DE NORMALIZACIÓN DE TEXTO ==========
export interface NormalizationOptions {
  removeAccents?: boolean;
  toLowerCase?: boolean;
  trimWhitespace?: boolean;
  removeSpecialChars?: boolean;
  maxLength?: number;
  preserveSpaces?: boolean;
}

export interface SanitizationOptions {
  maxLength?: number;
  removeHtmlChars?: boolean;
  removeScriptChars?: boolean;
  preserveBasicPunctuation?: boolean;
}

// ========== TIPOS DE TESTING Y SIMULACIÓN ==========
export interface TestScenario {
  id: string;
  name: string;
  description: string;
  inputs: string[];
  expectedOutputs: any[];
  timeout?: number;
}

export interface TestResult {
  scenarioId: string;
  scenarioName: string;
  totalTests: number;
  passed: number;
  failed: number;
  results: Array<{
    input: string;
    expected: any;
    actual: any;
    passed: boolean;
    confidence: number;
    duration: number;
  }>;
  duration: number;
  passRate: number;
}

export interface SpeechSimulator {
  simulateInput: (text: string, delay?: number) => Promise<void>;
  simulateSequence: (texts: string[], delay?: number) => Promise<void>;
  simulateWithNoise: (text: string, noiseLevel?: number) => Promise<void>;
  simulateWithAccent: (text: string, accent?: 'andaluz' | 'mexicano' | 'argentino') => Promise<void>;
  simulateInterruption: (text: string, interruptAt?: number) => Promise<void>;
  simulateHesitation: (text: string) => Promise<void>;
  simulateBackground: (text: string, background?: 'music' | 'traffic' | 'crowd') => Promise<void>;
  simulateVolume: (text: string, volume?: 'whisper' | 'normal' | 'loud') => Promise<void>;
}
