import type { ReactNode } from "react";
import type { AppConfig, AppError, AppState, GameFlowState } from "./app";
import type { GameSession, GamePhase, GameResponseType, GameMode, PlayerRole, GameInsight } from "./game";
import type { AudioState, SpeechOutputState } from "./audio";
import type { TranscriptionEvent } from "./speech";
import type { MHCState, MHCCapabilities } from "./services";

// ========== TIPOS DE EVENTOS ==========
export type EventType =
  | 'game:start'
  | 'game:end'
  | 'speech:input'
  | 'speech:output'
  | 'ui:navigate';

export interface GameEvent {
  type: EventType;
  payload?: any;
  timestamp: Date;
}

// ========== CONTEXTO DE APLICACIÓN ==========
export interface AppContextProps {
  // Estado de la aplicación
  appState: AppState;
  isInitialized: boolean;

  // Configuración
  config: AppConfig;
  updateConfig: (newConfig: Partial<AppConfig>) => void;

  // Manejo de errores
  errors: AppError[];
  addError: (error: AppError) => void;
  clearErrors: () => void;
  clearError: (errorId: string) => void;

  // Navegación
  currentView: string;
  navigate: (view: string) => void;

  // Inicialización
  initialize: () => Promise<void>;
  reset: () => void;
}

// ========== CONTEXTO DE JUEGO ==========
export interface EnygmaGameContextProps {
  // Estado actual
  session: GameSession | null;
  currentPhase: GamePhase;
  playerRole: PlayerRole;

  // Acciones del juego
  startNewGame: (mode: GameMode, character?: string) => Promise<void>;
  askQuestion: (question: string) => Promise<void>;
  respondToQuestion: (response: "yes" | "no" | "maybe" | "unknown") => Promise<void>;
  makeGuess: (character: string) => Promise<boolean>;
  endGame: (reason: "victory" | "defeat" | "timeout" | "quit") => void;

  // Análisis del juego
  getGameInsights: () => GameInsight;
  getSuggestedResponses: () => string[];

  // Propiedades computadas
  canAskQuestion: boolean;
  canMakeGuess: boolean;
  questionsRemaining: number;
  gameProgress: number;

  // Utilidades
  validateUserInput: (input: string) => { isValid: boolean; suggestion?: string };
  getHint: () => string | null;
}

// ========== CONTEXTO DE ORQUESTADOR ==========
export interface GameOrchestratorContextProps {
  // Estado de aplicación
  flowState: GameFlowState;
  isProcessing: boolean;

  // Control de flujo
  startGameFlow: () => Promise<void>;
  processUserInput: (input: string) => Promise<void>;
  handleGameEnd: () => Promise<void>;

  // Coordinación de servicios
  initializeServices: () => Promise<void>;
  resetServices: () => void;
}

// ========== CONTEXTO DE MHC ==========
export interface MHCContextProps {
  // Estado actual
  state: MHCState;

  // Funcionalidad core MHC
  setSucwTimeout: (timeMS: number) => void;
  closeWebView: () => void;
  hideAura: () => void;
  speakAura: (text: string) => void;
  sendAura: (text: string) => void;
  getId: () => string;

  // Operaciones mejoradas
  speakAndHide: (text: string, hideDelay?: number) => Promise<void>;
  setAppTimeout: (seconds: number) => void;

  // Gestión de conexión
  checkConnection: () => Promise<boolean>;
  reconnect: () => Promise<boolean>;
  getDeviceInfo: () => object;

  // Utilidades
  isFeatureAvailable: (feature: keyof MHCCapabilities) => boolean;
  testAllFeatures: () => Promise<MHCCapabilities>;
  reset: () => void;
}

// ========== CONTEXTO DE ENTRADA DE VOZ ==========
export interface SpeechInputState {
  transcription: string | null;
  isListening: boolean;
  isProcessing: boolean;
  confidence: number;
  lastValidatedResponse: GameResponseType | null;
  errorMessage: string | null;
}

export interface SpeechInputContextProps {
  // Estado actual
  state: SpeechInputState;

  // Controles básicos
  startListening: () => void;
  stopListening: () => void;
  clearTranscription: () => void;
  reset: () => void;

  // Testing y simulación
  simulateTranscription: (text: string) => void;

  // Funcionalidad específica del juego
  validateGameResponse: (text: string) => GameResponseType;
  waitForValidResponse: (timeout?: number) => Promise<GameResponseType>;
  waitForCustomResponse: (expectedResponses: string[], timeout?: number) => Promise<string>;

  // Manejo de eventos
  addEventListener: (callback: (event: TranscriptionEvent) => void) => () => void;

  // Utilidades
  normalizeText: (text: string) => Promise<string>;
  getSupportedResponses: () => string[];
  getResponseHelp: () => string;

  // Utilidades mejoradas
  getConfidenceScore: (text: string) => number;
  getAlternativeResponses: (text: string) => GameResponseType[];
  isResponseAmbiguous: (text: string) => boolean;
}

// ========== CONTEXTO DE SALIDA DE VOZ ==========
export interface SpeechOutputContextProps {
  // Estado actual
  state: SpeechOutputState;

  // Control de reproducción
  speak: (text: string, type?: string) => Promise<void>;
  stop: () => void;
  pause: () => void;
  resume: () => void;

  // Controles de música independientes
  playBackgroundMusic: () => Promise<void>;
  pauseMusic: () => void;
  resumeMusic: () => void;
  stopMusic: () => void;
  setMusicVolume: (volume: number) => void;

  // Controles globales
  muteAll: () => void;
  unmuteAll: () => void;
  pauseAll: () => void;
  resumeAll: () => void;
  stopAll: () => void;

  // Configuración
  configure: (gender: "male" | "female") => Promise<boolean>;
  setVoice: (voiceId: string) => Promise<void>;
  setGender: (gender: "male" | "female") => Promise<void>;

  // Control de audio
  setVolume: (volume: number) => void;
  mute: () => void;
  unmute: () => void;

  // Gestión de estado
  reset: () => void;
  initialize: () => Promise<void>;
}

// ========== CONTEXTO DE BUS DE EVENTOS ==========
export interface EventBusContextProps {
  emit: (type: EventType, payload?: any) => void;
  subscribe: (type: EventType, callback: (event: GameEvent) => void) => () => void;
  getLastEvent: (type: EventType) => GameEvent | null;
}

// ========== PROPS COMUNES DE CONTEXTOS ==========
export interface ContextProviderProps {
  children: ReactNode;
}
