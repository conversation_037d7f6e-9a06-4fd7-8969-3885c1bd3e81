/**
 * Optimiza el texto para mejorar la síntesis de voz
 */
export function optimizeTextForSpeech(text: string): string {
  return text
    .replace(/([.!?])\s+/g, '$1 ') // Añadir espacio después de signos de puntuación
    .trim();
}

/**
 * Valida que el texto sea apropiado para síntesis
 */
export function validateTextForSpeech(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  const trimmed = text.trim();
  if (trimmed.length === 0) {
    return false;
  }

  // Verificar que no sea solo espacios en blanco o caracteres especiales
  if (!/[a-zA-ZáéíóúñÁÉÍÓÚÑ0-9]/.test(trimmed)) {
    return false;
  }

  return true;
}
