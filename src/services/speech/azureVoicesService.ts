import axios from "axios";
import { handleRequest } from "../_helpersService";
import type {
  IVoicesService,
  AudioRequest,
  PerformanceStats,
} from "../../models/speech";
import {
  SPEECH_CONFIG,
  VOICE_DEFAULTS,
  validateConfig,
} from "./config";
import { AudioCache } from "./cache";
import { RetryQueue } from "./retryQueue";
import {
  optimizeTextForSpeech,
  validateTextForSpeech,
} from "./textUtils";

/**
 * Servicio de Azure Voices para síntesis de voz - CORREGIDO
 */
export class AzureVoicesService implements IVoicesService {
  private static instance: AzureVoicesService;

  // ===== CORE =====
  private voiceId: string = "";
  private genre: string = "";
  private availableVoicesList: string[] = [];

  // ===== SISTEMAS =====
  private audioCache: AudioCache;
  private retryQueue: RetryQueue;

  private constructor() {
    this.audioCache = new AudioCache();
    this.retryQueue = new RetryQueue();
  }

  public static getInstance(): AzureVoicesService {
    if (!AzureVoicesService.instance) {
      AzureVoicesService.instance = new AzureVoicesService();
    }
    return AzureVoicesService.instance;
  }

  // ========== GETTERS Y SETTERS ==========
  public getCurrentVoiceId(): string {
    return this.voiceId;
  }

  public getAvailableVoicesList(): string[] {
    return this.availableVoicesList;
  }

  private setVoiceId(voice: string): void {
    this.voiceId = voice;
  }

  private setGenre(genre: string): void {
    this.genre = genre;
  }

  // ========== CONFIGURACIÓN Y ESTADO ==========
  public reset(): void {
    console.log("ℹ️ [AzureVoicesService] 🔄 Reseteando servicio...");

    // Core
    this.voiceId = "";
    this.genre = "";
    this.availableVoicesList = [];

    // Sistemas
    this.audioCache.clear();
    this.retryQueue.clear();
  }

  // ========== HEADERS CORREGIDOS ==========
  private getHeaders(): Record<string, string> {
    if (!SPEECH_CONFIG.API_KEY) {
      throw new Error("VITE_SPEECH_API_KEY no está configurada");
    }

    // ✅ FORMATO CORRECTO: Solo Authorization, sin Content-Type ni Accept
    return {
      'Authorization': `Bearer ${SPEECH_CONFIG.API_KEY}`,
    };
  }

  // ========== API DE AZURE ==========
  public async getAvailableVoices(): Promise<string[]> {
    validateConfig();

    console.log('🔍 [AzureVoicesService] Obteniendo voces disponibles...');

    const data = {
      language: SPEECH_CONFIG.LANGUAGE,
      gender: this.genre
    };

    try {
      const response = await handleRequest<string[]>(
        axios.post(`${SPEECH_CONFIG.BASE_URL}available_voices`, data, {
          headers: this.getHeaders()
        })
      );

      console.log('✅ [AzureVoicesService] Voces obtenidas:', response);
      return response;
    } catch (error) {
      console.error('❌ [AzureVoicesService] Error obteniendo voces:', error);
      throw error;
    }
  }

  public async configVoice(genre: string): Promise<boolean> {
    this.setGenre(genre);

    try {
      const voices = await this.getAvailableVoices();
      this.availableVoicesList = voices;

      if (voices.length > 0) {
        const randomVoice = voices[Math.floor(Math.random() * voices.length)];
        this.setVoiceId(randomVoice);
        console.log(`✅ [AzureVoicesService] Voz configurada: ${randomVoice}`);
        return true;
      }

      console.warn("⚠️ [AzureVoicesService] No hay voces disponibles");
      return false;
    } catch (error) {
      console.error("❌ [AzureVoicesService] Error configurando voz", error);
      this.availableVoicesList = [];
      return false;
    }
  }

  // ========== MAIN - CORREGIDO CON FORMATO FUNCIONAL ==========
  public async getAudio(text: string): Promise<Blob> {
    validateConfig();

    if (!this.voiceId) {
      throw new Error("Azure Speech no configurado correctamente");
    }

    if (!validateTextForSpeech(text)) {
      throw new Error("Texto no válido para síntesis");
    }

    const cachedAudio = this.audioCache.get(text, this.voiceId);
    if (cachedAudio) {
      return cachedAudio;
    }

    const optimizedText = optimizeTextForSpeech(text);
    console.log(
      `🔍 [AzureVoicesService] Generando audio para: "${optimizedText.substring(0, 50)}..."`
    );

    return this.retryQueue.handleThrottling(async () => {
      // ✅ FORMATO CORRECTO QUE FUNCIONA
      const correctPayload = {
        input_text: optimizedText,
        voice_params: {
          voice_id: this.voiceId,
          rate: VOICE_DEFAULTS.RATE
        },
        output_format: VOICE_DEFAULTS.OUTPUT_FORMAT
      };

      console.log('🔍 [AzureVoicesService] Usando formato correcto:', correctPayload);

      try {
        const response = await handleRequest<Blob>(
          axios.post(`${SPEECH_CONFIG.BASE_URL}t2s`, correctPayload, {
            responseType: "blob",
            headers: this.getHeaders(), // Sin Content-Type ni Accept
          })
        );

        console.log(
          `✅ [AzureVoicesService] ¡ÉXITO! Audio generado (${response.size} bytes)`
        );

        this.audioCache.set(text, this.voiceId, response);
        return response;

      } catch (error) {
        console.error('❌ [AzureVoicesService] Error generando audio:', error);
        throw error;
      }
    });
  }

  // ========== UTILIDADES ==========
  public cleanupCache(): void {
    this.audioCache.cleanup();
  }

  public getPerformanceStats(): PerformanceStats {
    return {
      cache: this.audioCache.getStats(),
      queue: this.retryQueue.getStats(),
      voice: {
        configured: Boolean(this.voiceId),
        voiceId: this.voiceId,
        availableCount: this.availableVoicesList.length,
      },
    };
  }

  public async testAPIConnection(): Promise<void> {
    console.log(`🧪 [AzureVoicesService] Testing API connection...`);

    // Payload de prueba con formato correcto
    const testPayload = {
      input_text: "Hola mundo",
      voice_params: {
        voice_id: this.voiceId || "Elvira",
        rate: 1.1
      },
      output_format: "mp3"
    };

    try {
      const response = await axios.post(
        `${SPEECH_CONFIG.BASE_URL}t2s`,
        testPayload,
        {
          headers: this.getHeaders(),
          timeout: 5000,
          responseType: "blob"
        }
      );

      console.log(
        `✅ [AzureVoicesService] API conectada correctamente:`,
        response.status
      );
    } catch (error) {
      console.error(`❌ [AzureVoicesService] Error de conexión:`, error);

      if (axios.isAxiosError(error) && error.response?.data instanceof Blob) {
        const errorText = await error.response.data.text();
        console.error(`🔴 Error response from API:`, errorText);
      }
    }
  }
}
