import type { C<PERSON>Entry } from "../../models/speech";

import { SPEECH_CONFIG } from "./config";

/**
 * Sistema de cache para audio del servicio de Speech
 */
export class AudioCache {
  private cache = new Map<string, CacheEntry>();
  private readonly ttl: number;

  constructor(ttl: number = SPEECH_CONFIG.CACHE_TTL) {
    this.ttl = ttl;
  }

  /**
   * Genera una clave única para el cache
   */
  private getCacheKey(text: string, voiceId: string): string {
    return `${voiceId}-${text.toLowerCase().trim()}`;
  }

  /**
   * Obtiene audio del cache si está disponible y no ha expirado
   */
  public get(text: string, voiceId: string): Blob | null {
    const key = this.getCacheKey(text, voiceId);
    const cached = this.cache.get(key);

    if (cached && Date.now() - cached.timestamp < this.ttl) {
      console.log("🎯 Audio cache hit:", text.substring(0, 30));
      return cached.blob;
    }

    // Limpiar entrada expirada
    if (cached) {
      this.cache.delete(key);
    }

    return null;
  }

  /**
   * Guarda audio en el cache
   */
  public set(text: string, voiceId: string, blob: Blob): void {
    const key = this.getCacheKey(text, voiceId);
    this.cache.set(key, {
      blob,
      timestamp: Date.now(),
      voiceId
    });
  }

  /**
   * Limpia entradas expiradas del cache
   */
  public cleanup(): void {
    const now = Date.now();
    let removed = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
        removed++;
      }
    }

    if (removed > 0) {
      console.log(`ℹ️ [AudioCache] 🧹 Cache limpiado: ${removed} entradas removidas`);
    }
  }

  /**
   * Limpia todo el cache
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * Obtiene el tamaño actual del cache
   */
  public size(): number {
    return this.cache.size;
  }

  /**
   * Obtiene estadísticas del cache
   */
  public getStats() {
    return {
      size: this.cache.size,
      ttl: this.ttl,
      // TODO: Implementar tracking real de hits/misses
      hitRate: 0
    };
  }
}
