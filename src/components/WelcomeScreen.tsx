import { useState } from "react";
import { useGameOrchestrator } from "../contexts/GameOrchestratorContext";
import { PrimaryButton } from "microapps";

interface WelcomeScreenProps {
  onGameReady: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onGameReady }) => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [showSilentModeWarning, setShowSilentModeWarning] = useState(false);
  const { initializeApp, isSilentMode } = useGameOrchestrator();

  const handleStartGame = async () => {
    setIsInitializing(true);
    setShowSilentModeWarning(false);

    try {
      console.log("WelcomeScreen: 🚀 Iniciando inicialización completa...");

      // Usar la inicialización completa del GameOrchestrator
      // Esto incluye configuración de voz, mensaje de bienvenida y música
      await initializeApp();

      console.log("✅ WelcomeScreen: Inicialización completada");

      // Mostrar advertencia si estamos en modo silencioso
      if (isSilentMode) {
        console.warn("⚠️ WelcomeScreen: Modo silencioso activado - sin voz");
        setShowSilentModeWarning(true);
      }

      // Notificar que todo está listo
      onGameReady();
    } catch (error) {
      console.error("❌ WelcomeScreen: Error en inicialización:", error);
      // Aún así, permitir continuar - la app puede funcionar sin voz
      setShowSilentModeWarning(true);
      onGameReady();
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <div id="welcome-screen-overlay">
      <div className="welcome-screen">
        <h1>
          El velo del misterio se alza.
        </h1>

        <p>
          ¿Estás listo para enfrentarte a Enygma y desvelar el personaje oculto
          en el que está pensando?
        </p>

        <div className="text-center">
          <PrimaryButton
            onClick={handleStartGame}
            isDisabled={isInitializing}
            isLoading={isInitializing}
            text={isInitializing ? "Preparando..." : "Empezar"}
            backgroundColor="#88FFD5"
            textColor="#001428"
            borderRadius="8px"
          />
        </div>

        {isInitializing && (
          <p className="disclaimer-text">
            Configurando la experiencia mágica...
          </p>
        )}

        {showSilentModeWarning && (
          <div className="silent-mode-warning" style={{
            marginTop: '20px',
            padding: '10px',
            backgroundColor: 'rgba(255, 200, 0, 0.2)',
            borderRadius: '8px',
            fontSize: '14px'
          }}>
            <p style={{ margin: 0 }}>
              <span role="img" aria-label="warning">⚠️</span> Modo silencioso activado: La aplicación funcionará sin voz.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
