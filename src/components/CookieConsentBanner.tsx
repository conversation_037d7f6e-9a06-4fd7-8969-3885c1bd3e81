// components/CookieConsentBanner.tsx - MODIFICADO
import { useState, useEffect, useCallback } from "react";
import { useSpeechOutput } from "../contexts/SpeechOutputContext";

interface CookieConsentBannerProps {
  onAudioActivated?: () => void;
  onConsentGiven?: () => void; // 🆕 Nuevo callback para cualquier respuesta de consentimiento
}

// Custom hook para manejar localStorage
const useCookieStorage = () => {
  const getItem = (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  };

  const setItem = (key: string, value: string): void => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn(`No se pudo guardar ${key} en localStorage:`, error);
    }
  };

  return { getItem, setItem };
};

// Constantes para evitar magic strings
const STORAGE_KEYS = {
  ANALYTICS_CONSENT: "enygma_analytics_consent",
  ANALYTICS_TIMESTAMP: "enygma_analytics_timestamp",
  AUDIO_ACTIVATED: "enygma_audio_activated",
} as const;

const CONSENT_VALUES = {
  ACCEPTED: "accepted",
  REJECTED: "rejected",
} as const;

export const CookieConsentBanner: React.FC<CookieConsentBannerProps> = ({
  onAudioActivated,
  onConsentGiven, // 🆕 Nuevo prop
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isActivatingAudio, setIsActivatingAudio] = useState(false);

  const { getItem, setItem } = useCookieStorage();

  const {
    speakGameMessage,
    configure,
    state: { isReady: isLocutionActivated }
  } = useSpeechOutput();

  // Lógica para determinar visibilidad
  useEffect(() => {
    const hasConsent = getItem(STORAGE_KEYS.ANALYTICS_CONSENT);

    if (!hasConsent) {
      const timer = setTimeout(() => setIsVisible(true), 1500);
      return () => clearTimeout(timer);
    }
  }, [getItem]);

  // Función para guardar consentimiento
  const saveConsentPreferences = useCallback((acceptAnalytics: boolean) => {
    const consentValue = acceptAnalytics ? CONSENT_VALUES.ACCEPTED : CONSENT_VALUES.REJECTED;

    setItem(STORAGE_KEYS.ANALYTICS_CONSENT, consentValue);
    setItem(STORAGE_KEYS.ANALYTICS_TIMESTAMP, new Date().toISOString());

    console.log(`🍪 Analíticas ${acceptAnalytics ? 'aceptadas' : 'rechazadas'}`);
  }, [setItem]);

  const activateAudio = useCallback(async () => {
    if (!isLocutionActivated) {
      await configure("female");
    }

    setItem(STORAGE_KEYS.AUDIO_ACTIVATED, "true");

    onAudioActivated?.();
  }, [isLocutionActivated, configure, speakGameMessage, onAudioActivated, setItem]);

  const activateAudioWithConsent = useCallback(
    async (acceptAnalytics: boolean) => {
      setIsActivatingAudio(true);

      try {
        saveConsentPreferences(acceptAnalytics);

        if (acceptAnalytics) {
          // Solo activar audio si acepta las analíticas
          await activateAudio();
        } else {
          // Si rechaza, solo guardar preferencias y continuar
          onConsentGiven?.(); // 🆕 Notificar que se dio consentimiento (aunque sea rechazo)
        }

        setIsVisible(false);
      } catch (error) {
        console.error("❌ Error procesando consentimiento:", error);
        setIsVisible(false);
      } finally {
        setIsActivatingAudio(false);
      }
    },
    [saveConsentPreferences, activateAudio, onConsentGiven]
  );

  // 🔧 MODIFICADO: Ahora también usa el callback común
  const handleReject = useCallback(() => {
    activateAudioWithConsent(false);
  }, [activateAudioWithConsent]);

  const handleAccept = useCallback(() => {
    activateAudioWithConsent(true);
  }, [activateAudioWithConsent]);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          zIndex: 9998,
          backdropFilter: "blur(2px)",
        }}
        onClick={handleReject}
        aria-label="Cerrar banner de consentimiento"
      />

      {/* Banner */}
      <div
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "90%",
          maxWidth: "480px",
          backgroundColor: "#1e293b",
          borderRadius: "12px",
          padding: "32px",
          boxShadow: "0 20px 60px rgba(0, 0, 0, 0.3)",
          zIndex: 9999,
          animation: "slideUp 0.5s ease-out",
          textAlign: "center",
        }}
        onClick={(e) => e.stopPropagation()}
        role="dialog"
        aria-modal="true"
        aria-labelledby="cookie-title"
        aria-describedby="cookie-description"
      >
        {/* Título */}
        <h2
          id="cookie-title"
          style={{
            margin: "0 0 16px 0",
            fontSize: "24px",
            fontWeight: "600",
            color: "#ffffff",
            lineHeight: "1.3",
          }}
        >
          Ayúdanos a mejorar
        </h2>

        {/* Descripción */}
        <p
          id="cookie-description"
          style={{
            margin: "0 0 32px 0",
            fontSize: "16px",
            color: "#94a3b8",
            lineHeight: "1.5",
            maxWidth: "400px",
            marginLeft: "auto",
            marginRight: "auto",
          }}
        >
          Al aceptar nos ayudas a mejorar la experiencia permitiendo el análisis
          estadístico de datos de uso.
        </p>

        {/* Botones */}
        <div
          style={{
            display: "flex",
            gap: "16px",
            justifyContent: "center",
            flexDirection: window.innerWidth < 480 ? "column" : "row",
          }}
        >
          {/* Botón Rechazar */}
          <button
            onClick={handleReject}
            disabled={isActivatingAudio}
            aria-label="Rechazar cookies analíticas"
            style={{
              minWidth: "120px",
              backgroundColor: "transparent",
              color: "#ffffff",
              border: "2px solid #475569",
              borderRadius: "6px",
              padding: "12px 24px",
              fontSize: "16px",
              fontWeight: "500",
              cursor: isActivatingAudio ? "not-allowed" : "pointer",
              transition: "all 0.2s ease",
              opacity: isActivatingAudio ? 0.5 : 1,
            }}
          >
            Rechazar
          </button>

          {/* Botón Aceptar */}
          <button
            onClick={handleAccept}
            disabled={isActivatingAudio}
            aria-label="Aceptar cookies analíticas y activar audio"
            style={{
              minWidth: "120px",
              backgroundColor: isActivatingAudio ? "#1e40af" : "#2563eb",
              color: "#ffffff",
              border: "none",
              borderRadius: "6px",
              padding: "12px 24px",
              fontSize: "16px",
              fontWeight: "500",
              cursor: isActivatingAudio ? "not-allowed" : "pointer",
              transition: "background-color 0.2s ease",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "8px",
            }}
          >
            {isActivatingAudio ? (
              <>
                <div
                  style={{
                    width: "16px",
                    height: "16px",
                    border: "2px solid #ffffff30",
                    borderTop: "2px solid #ffffff",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite",
                  }}
                />
                Activando...
              </>
            ) : (
              "Aceptar"
            )}
          </button>
        </div>
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes slideUp {
          from {
            transform: translate(-50%, -50%) translateY(100%);
            opacity: 0;
          }
          to {
            transform: translate(-50%, -50%) translateY(0);
            opacity: 1;
          }
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </>
  );
};
