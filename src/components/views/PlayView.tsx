import { useState, useEffect, useRef } from "react";
import { useEnygmaGame } from "../../contexts/EnygmaGameContext";
import "./PlayView.scss";

interface PlayViewProps {
  handleShowLives: () => void;
  handleShowClues: () => void;
  handleExistGame: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowLives,
  handleShowClues,
  handleExistGame,
}) => {
  const { session, askQuestion } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map((msg: SessionMessage): ChatMessage => ({
        id: msg.id,
        text: msg.text,
        sender: msg.sender,
        timestamp: msg.timestamp,
      }));
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  // Add initial welcome message if no session messages yet
  useEffect(() => {
    if (session && (!session.messages || session.messages.length === 0) && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: "welcome",
        text: session.mode === "player_vs_ia"
          ? "¡Perfecto! Piensa en un personaje del entretenimiento. Yo haré preguntas y tú respondes con Sí, No, Tal vez o No lo sé."
          : "¡Genial! Heeeee pensado en un personaje. Haz preguntas que pueda responder con Sí, No, Tal vez o No lo sé.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages([welcomeMessage]);
    }
  }, [session, messages.length]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      // Send question to AI through game context
      await askQuestion(messageText);
    } catch (error) {
      // console.error("Error sending message:", error);
      // Add error message
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: "Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="content">
      <div className="menu-left">
        <div>
          <img src="assets/game/enygma.png" alt="Enygma" className="enygma-image" />
        </div>
      </div>

      <div className="game chat-view">
        <div className="chat-container">
          <div className="messages-list">
            {messages.length === 0 && (
              <div className="welcome-message">
                <p>¡Hola! Soy Enygma. Estoy listo para jugar contigo.</p>
                <p>
                  {session?.mode === "player_vs_ia"
                    ? "Piensa en un personaje y yo intentaré adivinarlo haciendo preguntas."
                    : "Yo pensaré en un personaje y tú tendrás que adivinarlo."}
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.sender === "user" ? "user-message" : "ai-message"}`}
              >
                <div className="message-content">
                  <span className="message-text">{message.text}</span>
                  <span className="message-time">
                    {message.timestamp.toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </span>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="message ai-message">
                <div className="message-content">
                  <span className="message-text typing">Enygma está pensando...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        <div className="chat-input-container">
          <div className="input-wrapper">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                session?.mode === "player_vs_ia"
                  ? "Responde con 'Sí', 'No', 'Tal vez' o 'No lo sé'..."
                  : "Haz una pregunta sobre el personaje..."
              }
              disabled={isLoading || !session}
              className="chat-input"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputText.trim() || isLoading || !session}
              className="send-button"
            >
              {isLoading ? "Enviando..." : "Enviar"}
            </button>
          </div>
        </div>
      </div>

      <div className="menu-right">
        <div onClick={handleShowLives} style={{ cursor: "pointer" }}>
          <img
            src="assets/game/lives.png"
            alt="Vidas"
            className="lives-image"
          />

          {session && (
            <div>{session.questionCount}/{session.maxQuestions}</div>
          )}
        </div>

        <div onClick={handleShowClues} style={{ cursor: "pointer" }}>
          <img
            src="assets/game/clues.png"
            alt="Pistas"
            className="clues-image"
          />
          <div>Pistas</div>
        </div>

        <div onClick={handleExistGame} style={{ cursor: "pointer" }}>
          <img src="assets/game/exit.png" alt="Salir" className="exit-image" />
          <div>Salir</div>
        </div>
      </div>
    </div>
  );
};

export default PlayView;
