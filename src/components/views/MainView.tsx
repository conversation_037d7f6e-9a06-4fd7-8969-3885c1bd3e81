import { useState, useEffect } from "react";

interface GameMode {
  id: string;
  enabled: boolean;
  image: string;
  mode: "player_vs_ia" | "ia_vs_player";
  buttonText: string;
  description: string;
}

interface MainViewProps {
  handleStartGame: (mode: "player_vs_ia" | "ia_vs_player") => Promise<void>;
  handleShowRules: () => void;
  isStartingGame: boolean;
  isReady: boolean;
}

const MainView: React.FC<MainViewProps> = ({
  handleStartGame,
  handleShowRules,
  isStartingGame,
  isReady,
}) => {
  const [gameModes, setGameModes] = useState<GameMode[]>([]);

  useEffect(() => {
    const loadGameModes = async () => {
      try {
        const response = await fetch('/game-modes.json');
        const data = await response.json();
        setGameModes(data.gameModes || []);
      } catch (error) {
        // console.error('Error loading game modes:', error);
      }
    };

    loadGameModes();
  }, []);

  return (
    <div className="content">
      <div className="menu-left">
        <div onClick={handleShowRules} style={{ cursor: "pointer" }}>
          <img src="assets/game/book.png" alt="Book" className="book-image" />
          <div>Reglas</div>
        </div>
      </div>

      <div className="game">
        <div className="game-header">
          <h1>Enygma</h1>
          <p>¿Puedes adivinar el personaje que está pensando Enygma?</p>
        </div>

        <div className="game-mode">
          {gameModes
            .filter(mode => mode.enabled)
            .map((mode) => (
              <div key={mode.id} className="enygma-wrapper">
                <img
                  src={mode.image}
                  alt="Enygma"
                  className="enygma-image"
                />

                <p>{mode.description}</p>

                <button
                  onClick={() => handleStartGame(mode.mode)}
                  disabled={isStartingGame || !isReady}
                >
                  {isStartingGame
                    ? "Iniciando..."
                    : !isReady
                      ? "Preparando..."
                      : mode.buttonText}
                </button>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default MainView;
