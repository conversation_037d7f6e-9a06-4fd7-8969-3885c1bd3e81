import "./LivesView.scss";

const LivesView: React.FC = ({}) => {
  return (
    <div className="content lives-modal">
      <div className="lives-wrapper">
        <div className="game-header">
          <h1>Tus preguntas restantes</h1>
        </div>

        <div className="lives-container">
          <div>
            Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
            vez que haces una, se descuenta del contador. Piensa bien cada
            pregunta: ¡cada una cuenta!
          </div>
        </div>
      </div>
    </div>
  );
};

export default LivesView;
