.header {
  height: calc(105px - 32px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;

  .header-title {
    // font-family: Playfair Display;
    font-weight: 600;
    font-style: SemiBold;
    font-size: 24px;
    line-height: 180%;
    letter-spacing: 0%;
    color: #88ffd5;
  }

  .header-right {
    display: flex;
    gap: 32px;

    .sound-icon {
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 4px;
      border-radius: 6px;
      margin-left: 8px;

      &.muted {
        opacity: 0.8;
      }
    }

    .speech-control.no-audio {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        transform: none;
        background: rgba(255, 136, 213, 0.1);
      }
    }

    .audio-indicator {
      position: absolute;
      top: -4px;
      right: -4px;
      display: flex;
      gap: 2px;

      .music-indicator,
      .speech-indicator {
        background: #88ffd5;
        color: #0b2739;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        font-weight: bold;
      }
    }
  }
}

.audio-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;

  .audio-menu {
    background: #0b2739;
    border: 2px solid #88FFD5;
    border-radius: 12px;
    padding: 24px;
    min-width: 500px;
    max-width: 600px;

    h3 {
      margin: 0 0 20px 0;
      color: #88FFD5;
      text-align: center;
    }

    .audio-actions {
      display: flex;
      gap: 12px;
      margin-top: 20px;
    }

    button {
      flex: 1;
      padding: 10px 16px;
      border: 1px solid #88FFD5;
      border-radius: 6px;
      background: transparent;
      color: #88FFD5;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(136, 255, 213, 0.1);
      }

      &.mute {
        background: rgba(255, 100, 100, 0.2);
        border-color: #ff6464;
        color: #ff6464;
      }

      &.unmute {
        background: rgba(136, 255, 213, 0.2);
        color: #88FFD5;
      }
    }
  }
}
