import { useState } from "react";
import { useSpeechOutput } from "../../contexts/SpeechOutputContext";
import IconGoBack from "../icons/GoBack/IconGoBack";
import IconHome from "../icons/Home/IconHome";
import IconMenu from "../icons/Menu/IconMenu";
import IconSoundOn from "../icons/SoundOn/IconSoundOn";
import IconSoundOff from "../icons/SoundOff/IconSoundOff";
import IconMicrophoneOn from "../icons/MicrophoneOn/IconMicrophoneOn";
import IconMicrophoneOff from "../icons/MicrophoneOff/IconMicrophoneOff";
import "./Header.scss";

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  onGoHome?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  onGoHome,
  showBackButton = false,
}) => {
  const {
    state: { audioState, isMusicPlaying, isSpeechPlaying },
    toggleMute,
    pauseMusic,
    resumeMusic,
    pauseSpeech,
    resumeSpeech,
  } = useSpeechOutput();
  const [showAudioMenu, setShowAudioMenu] = useState(false);

  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  };

  const handleMusicClick = () => {
    if (isMusicPlaying) {
      pauseMusic();
    } else {
      resumeMusic();
    }
  };

  const handleSpeechClick = () => {
    if (isSpeechPlaying) {
      pauseSpeech();
    } else {
      const audioManager = (window as any).audioManager;
      if (audioManager && audioManager.hasSpeechAudio()) {
        resumeSpeech();
      } else {
        console.warn("No hay audio de narración disponible para reproducir");
      }
    }
  };

  const handleAdvancedMenu = () => {
    setShowAudioMenu(true);
  };

  // Verificar si hay audio de speech disponible
  const hasSpeechAudio =
    (window as any).audioManager?.hasSpeechAudio?.() || false;

  return (
    <>
      <div className="header">
        <div className="header-left">
          {currentView === "main" && <IconMenu />}

          {showBackButton && currentView !== "main" && onBackToMain && (
            <div className="back-button" onClick={onBackToMain}>
              <IconGoBack />
            </div>
          )}
        </div>

        <div className="header-title">{renderTitle()}</div>

        <div className="header-right">
          <div
            className={`sound-icon music-control ${audioState.isMuted ? "muted" : ""} ${isMusicPlaying ? "active" : ""}`}
            onClick={handleMusicClick}
            title={
              audioState.isMuted
                ? "Música silenciada"
                : isMusicPlaying
                  ? "Pausar música"
                  : "Reanudar música"
            }
          >
            {audioState.isMuted ? <IconSoundOff /> : <IconSoundOn />}

            {isMusicPlaying && !audioState.isMuted && (
              <div className="audio-indicator">
                <div className="music-indicator">♪</div>
              </div>
            )}
          </div>

          <div
            className={`sound-icon speech-control ${audioState.isMuted ? "muted" : ""} ${isSpeechPlaying ? "active" : ""} ${!hasSpeechAudio && !isSpeechPlaying ? "no-audio" : ""}`}
            onClick={handleSpeechClick}
            title={
              audioState.isMuted
                ? "Narración silenciada"
                : isSpeechPlaying
                  ? "Pausar narración"
                  : hasSpeechAudio
                    ? "Reanudar narración"
                    : "No hay narración disponible"
            }
          >
            {audioState.isMuted ? <IconMicrophoneOff /> : <IconMicrophoneOn />}

            {isSpeechPlaying && !audioState.isMuted && (
              <div className="audio-indicator">
                <div className="speech-indicator">💬</div>
              </div>
            )}
          </div>

          <div
            className="sound-icon menu-control"
            onClick={handleAdvancedMenu}
            title="Configuración avanzada de audio"
          >
            <IconMenu />
          </div>

          <div className="home-icon" onClick={onGoHome}>
            <IconHome />
          </div>
        </div>
      </div>

      {showAudioMenu && (
        <div
          className="audio-menu-overlay"
          onClick={() => setShowAudioMenu(false)}
        >
          <div className="audio-menu" onClick={(e) => e.stopPropagation()}>
            <h3>Control de Audio</h3>

            <div className="audio-actions">
              <button
                onClick={() => {
                  toggleMute();
                  setShowAudioMenu(false);
                }}
                className={audioState.isMuted ? "unmute" : "mute"}
              >
                {audioState.isMuted ? "🔊 Activar Audio" : "🔇 Silenciar Todo"}
              </button>

              <button onClick={() => setShowAudioMenu(false)}>Cerrar</button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
