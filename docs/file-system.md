.
├── docs
│   ├── diseño
│   │   ├── 01.png
│   │   ├── 02.png
│   │   ├── 03.png
│   │   ├── 04.png
│   │   ├── 05.png
│   │   ├── 06.png
│   │   ├── 07.png
│   │   ├── 08.png
│   │   ├── 09.png
│   │   └── 10.png
│   ├── figma.md
│   ├── file-system.md
│   └── prompt.md
├── .env
├── eslint.config.js
├── .gitignore
├── index.html
├── package.json
├── package-lock.json
├── public
│   ├── assets
│   │   ├── favicon.png
│   │   ├── fonts
│   │   │   ├── OnAir-BlackItalic.ttf
│   │   │   ├── OnAir-Black.ttf
│   │   │   ├── OnAir-BoldItalic.ttf
│   │   │   ├── OnAir-Bold.ttf
│   │   │   ├── OnAir-Italic.ttf
│   │   │   ├── OnAir-LightItalic.ttf
│   │   │   ├── OnAir-Light.ttf
│   │   │   ├── OnAirOutlineOne.ttf
│   │   │   ├── OnAirOutlineThree.ttf
│   │   │   ├── OnAirOutlineTwo.ttf
│   │   │   └── OnAir-Regular.ttf
│   │   ├── game
│   │   │   ├── background.png
│   │   │   ├── book_1.png
│   │   │   ├── book_2.png
│   │   │   ├── book.png
│   │   │   ├── clues.png
│   │   │   ├── enygma.png
│   │   │   ├── exit.png
│   │   │   ├── lives.png
│   │   │   └── player.png
│   │   └── sounds
│   │       ├── sound_1.mp3
│   │       ├── sound_3.mp3
│   │       └── sound.mp3
│   ├── game-modes.json
│   ├── game-rules.json
│   └── sdk.js
├── README.md
├── src
│   ├── App.scss
│   ├── App.tsx
│   ├── components
│   │   ├── CookieConsentBanner.tsx
│   │   ├── Header
│   │   │   ├── Header.scss
│   │   │   └── Header.tsx
│   │   ├── icons
│   │   │   ├── GoBack
│   │   │   │   └── IconGoBack.tsx
│   │   │   ├── Home
│   │   │   │   └── IconHome.tsx
│   │   │   ├── Menu
│   │   │   │   └── IconMenu.tsx
│   │   │   └── SoundOn
│   │   │       └── IconSoundOn.tsx
│   │   ├── views
│   │   │   ├── CluesView.scss
│   │   │   ├── CluesView.tsx
│   │   │   ├── LivesView.scss
│   │   │   ├── LivesView.tsx
│   │   │   ├── MainView.tsx
│   │   │   ├── PlayView.scss
│   │   │   ├── PlayView.tsx
│   │   │   ├── RulesView.scss
│   │   │   └── RulesView.tsx
│   │   └── WelcomeScreen.tsx
│   ├── contexts
│   │   ├── AppContext.tsx
│   │   ├── EnygmaGameContext.tsx
│   │   ├── EventBusContext.tsx
│   │   ├── GameOrchestratorContext.tsx
│   │   ├── MHCContext.tsx
│   │   ├── SpeechInputContext.tsx
│   │   └── SpeechOutputContext.tsx
│   ├── hooks
│   │   ├── useGameResponseValidator.ts
│   │   ├── useSpeechCoordinator.ts
│   │   └── useSpeechTesting.ts
│   ├── index.scss
│   ├── main.tsx
│   ├── models
│   │   ├── audio.ts
│   │   ├── general.ts
│   │   └── speech.ts
│   ├── services
│   │   ├── AIService.ts
│   │   ├── AudioManager.ts
│   │   ├── GameResponseValidationService.ts
│   │   ├── _helpersService.ts
│   │   ├── LogService.ts
│   │   ├── speech
│   │   │   ├── azureVoicesService.ts
│   │   │   ├── cache.ts
│   │   │   ├── config.ts
│   │   │   ├── index.ts
│   │   │   ├── retryQueue.ts
│   │   │   ├── speechService.ts
│   │   │   └── textUtils.ts
│   │   ├── SpeechCoordinator.ts
│   │   ├── SpeechService.ts
│   │   ├── TextNormalizationService.ts
│   │   └── TranscriptionService.ts
│   ├── _variables.scss
│   └── vite-env.d.ts
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.ts

22 directories, 103 files
